# Module Map and API Specification - Exchange Accounting System

## Overview

This document defines all system modules, their responsibilities, inputs/outputs, and API endpoints. The system follows a microservices architecture with clear separation of concerns and well-defined interfaces.

## Module Architecture

### 1. Authentication Module

**Responsibilities:**
- User authentication and authorization
- JWT token management
- Role-based access control
- Session management
- Password policies

**Inputs:**
- User credentials (username/password)
- JWT tokens for validation
- Role assignment requests

**Outputs:**
- JWT access and refresh tokens
- User profile information
- Authorization decisions
- Session status

**API Endpoints:**

```
POST /api/v1/auth/login
Body: { "username": "string", "password": "string" }
Response: { "access_token": "string", "refresh_token": "string", "user": {...} }

POST /api/v1/auth/refresh
Body: { "refresh_token": "string" }
Response: { "access_token": "string" }

POST /api/v1/auth/logout
Headers: Authorization: Bearer <token>
Response: { "message": "Logged out successfully" }

GET /api/v1/auth/profile
Headers: Authorization: Bearer <token>
Response: { "user": {...}, "permissions": [...] }

PUT /api/v1/auth/change-password
Body: { "current_password": "string", "new_password": "string" }
Response: { "message": "Password changed successfully" }
```

### 2. Customer Management Module

**Responsibilities:**
- Customer CRUD operations
- Customer profile management
- WhatsApp group integration
- Customer search and filtering

**Inputs:**
- Customer registration data
- Profile update requests
- Search criteria
- WhatsApp group information

**Outputs:**
- Customer profiles
- Customer lists with pagination
- Search results
- WhatsApp integration status

**API Endpoints:**

```
GET /api/v1/customers
Query: ?page=1&limit=20&search=string&location=uuid
Response: { "customers": [...], "total": number, "page": number }

POST /api/v1/customers
Body: { "first_name": "string", "last_name": "string", "phone": "string", ... }
Response: { "customer": {...}, "customer_code": "string" }

GET /api/v1/customers/{id}
Response: { "customer": {...}, "balances": [...], "recent_transactions": [...] }

PUT /api/v1/customers/{id}
Body: { "first_name": "string", "last_name": "string", ... }
Response: { "customer": {...} }

DELETE /api/v1/customers/{id}
Response: { "message": "Customer deleted successfully" }

POST /api/v1/customers/{id}/whatsapp-group
Body: { "group_id": "string", "group_name": "string" }
Response: { "message": "WhatsApp group linked successfully" }
```

### 3. Transaction Management Module

**Responsibilities:**
- Transaction processing and validation
- Multi-step transaction support
- Transaction status management
- Fee calculation and application
- Transaction search and filtering

**Inputs:**
- Transaction creation requests
- Transaction updates
- Status change requests
- Search and filter criteria

**Outputs:**
- Transaction records
- Transaction status updates
- Validation results
- Transaction summaries

**API Endpoints:**

```
GET /api/v1/transactions
Query: ?page=1&limit=20&customer_id=uuid&type=string&status=string&date_from=date&date_to=date
Response: { "transactions": [...], "total": number, "page": number }

POST /api/v1/transactions
Body: { "type": "buy|sell|transfer|delivery", "customer_id": "uuid", "amount": number, ... }
Response: { "transaction": {...}, "transaction_number": "string" }

GET /api/v1/transactions/{id}
Response: { "transaction": {...}, "lines": [...], "receipts": [...], "ledger_entries": [...] }

PUT /api/v1/transactions/{id}
Body: { "status": "string", "notes": "string", ... }
Response: { "transaction": {...} }

POST /api/v1/transactions/{id}/lines
Body: { "description": "string", "amount": number, "currency_id": "uuid", ... }
Response: { "line": {...} }

PUT /api/v1/transactions/{id}/status
Body: { "status": "completed|cancelled|failed", "reason": "string" }
Response: { "transaction": {...} }

POST /api/v1/transactions/{id}/receipts
Body: FormData with file upload
Response: { "receipt": {...} }
```

### 4. Ledger Management Module

**Responsibilities:**
- Double-entry bookkeeping
- Ledger entry creation and validation
- Balance calculations
- Account management
- Audit trail maintenance

**Inputs:**
- Transaction data for ledger entries
- Balance inquiry requests
- Account setup requests
- Audit queries

**Outputs:**
- Ledger entries
- Account balances
- Balance histories
- Audit reports

**API Endpoints:**

```
GET /api/v1/ledger/entries
Query: ?account_code=string&customer_id=uuid&date_from=date&date_to=date
Response: { "entries": [...], "total": number }

POST /api/v1/ledger/entries
Body: { "transaction_id": "uuid", "account_code": "string", "debit_amount": number, ... }
Response: { "entry": {...} }

GET /api/v1/ledger/balances
Query: ?location_id=uuid&currency_id=uuid&customer_id=uuid
Response: { "balances": [...] }

GET /api/v1/ledger/trial-balance
Query: ?location_id=uuid&date=date
Response: { "accounts": [...], "total_debits": number, "total_credits": number }

GET /api/v1/ledger/customer-balance/{customer_id}
Query: ?currency_id=uuid&location_id=uuid
Response: { "balances": [...], "total_balance": number }
```

### 5. Exchange Rate Module

**Responsibilities:**
- Exchange rate management
- Rate history tracking
- Rate change notifications
- Multi-location rate support

**Inputs:**
- Rate update requests
- Rate inquiry requests
- Historical rate queries
- Rate change thresholds

**Outputs:**
- Current exchange rates
- Historical rate data
- Rate change notifications
- Rate comparison reports

**API Endpoints:**

```
GET /api/v1/exchange-rates
Query: ?from_currency=string&to_currency=string&location_id=uuid&date=date
Response: { "rates": [...] }

POST /api/v1/exchange-rates
Body: { "from_currency_id": "uuid", "to_currency_id": "uuid", "buy_rate": number, "sell_rate": number, ... }
Response: { "rate": {...} }

GET /api/v1/exchange-rates/current
Query: ?location_id=uuid
Response: { "rates": [...] }

GET /api/v1/exchange-rates/history
Query: ?from_currency=string&to_currency=string&date_from=date&date_to=date
Response: { "rates": [...] }

PUT /api/v1/exchange-rates/{id}
Body: { "buy_rate": number, "sell_rate": number }
Response: { "rate": {...} }
```

### 6. Reporting Module

**Responsibilities:**
- Statement generation
- Report creation and export
- Data aggregation
- Custom report builder
- PDF/Excel export

**Inputs:**
- Report parameters and filters
- Export format preferences
- Custom report definitions
- Date ranges and criteria

**Outputs:**
- Generated reports
- Export files (PDF/Excel)
- Report metadata
- Aggregated data

**API Endpoints:**

```
POST /api/v1/reports/statement-of-account
Body: { "customer_id": "uuid", "date_from": "date", "date_to": "date", "currency_id": "uuid" }
Response: { "statement": {...}, "transactions": [...], "balances": {...} }

POST /api/v1/reports/branch-statement
Body: { "location_id": "uuid", "date_from": "date", "date_to": "date" }
Response: { "statement": {...}, "balances": [...], "allocations": [...] }

POST /api/v1/reports/export
Body: { "report_type": "string", "format": "pdf|excel", "parameters": {...} }
Response: { "file_url": "string", "expires_at": "datetime" }

GET /api/v1/reports/dashboard
Query: ?location_id=uuid&date_from=date&date_to=date
Response: { "summary": {...}, "charts": [...], "metrics": {...} }

POST /api/v1/reports/custom
Body: { "query": "string", "parameters": {...}, "format": "json|csv|excel" }
Response: { "data": [...], "metadata": {...} }
```

### 7. Notification Module

**Responsibilities:**
- WhatsApp message preparation
- Email notifications
- System alerts
- Rate change notifications
- Delivery status updates

**Inputs:**
- Notification triggers
- Message templates
- Recipient information
- Notification preferences

**Outputs:**
- Prepared messages
- Notification status
- Delivery confirmations
- Alert summaries

**API Endpoints:**

```
POST /api/v1/notifications/whatsapp/prepare
Body: { "customer_id": "uuid", "template": "string", "parameters": {...} }
Response: { "message": "string", "preview": "string", "group_id": "string" }

POST /api/v1/notifications/email
Body: { "to": "string", "subject": "string", "template": "string", "parameters": {...} }
Response: { "message_id": "string", "status": "sent|queued|failed" }

GET /api/v1/notifications/alerts
Query: ?type=string&status=string&date_from=date
Response: { "alerts": [...], "unread_count": number }

POST /api/v1/notifications/rate-alert
Body: { "currency_pair": "string", "threshold": number, "type": "increase|decrease" }
Response: { "alert": {...} }

PUT /api/v1/notifications/alerts/{id}/read
Response: { "message": "Alert marked as read" }
```

### 8. File Management Module

**Responsibilities:**
- File upload and storage
- Receipt management
- Document organization
- File security and access control

**Inputs:**
- File uploads
- File metadata
- Access permissions
- Storage preferences

**Outputs:**
- File URLs and metadata
- Upload confirmations
- File lists and searches
- Access tokens

**API Endpoints:**

```
POST /api/v1/files/upload
Body: FormData with file
Response: { "file": {...}, "url": "string", "file_id": "uuid" }

GET /api/v1/files/{id}
Response: File download or { "url": "string", "metadata": {...} }

GET /api/v1/files
Query: ?transaction_id=uuid&type=string&date_from=date
Response: { "files": [...], "total": number }

DELETE /api/v1/files/{id}
Response: { "message": "File deleted successfully" }

POST /api/v1/files/{id}/access-token
Body: { "expires_in": number }
Response: { "access_token": "string", "expires_at": "datetime" }
```

### 9. Location Management Module

**Responsibilities:**
- Branch/location management
- Location-specific configurations
- Multi-location operations
- Location-based reporting

**Inputs:**
- Location creation/update requests
- Configuration changes
- Location queries

**Outputs:**
- Location information
- Configuration settings
- Location lists
- Location-specific data

**API Endpoints:**

```
GET /api/v1/locations
Response: { "locations": [...] }

POST /api/v1/locations
Body: { "code": "string", "name": "string", "city": "string", "country": "string", ... }
Response: { "location": {...} }

GET /api/v1/locations/{id}
Response: { "location": {...}, "balances": [...], "recent_activity": [...] }

PUT /api/v1/locations/{id}
Body: { "name": "string", "address": "string", ... }
Response: { "location": {...} }

GET /api/v1/locations/{id}/balances
Response: { "balances": [...], "total_value": {...} }
```

### 10. Currency Management Module

**Responsibilities:**
- Currency configuration
- Currency conversion
- Multi-currency support
- Currency-specific settings

**Inputs:**
- Currency setup requests
- Conversion requests
- Currency queries

**Outputs:**
- Currency information
- Conversion results
- Currency lists
- Exchange calculations

**API Endpoints:**

```
GET /api/v1/currencies
Response: { "currencies": [...] }

POST /api/v1/currencies
Body: { "code": "string", "name": "string", "symbol": "string", "decimal_places": number }
Response: { "currency": {...} }

GET /api/v1/currencies/{id}
Response: { "currency": {...} }

PUT /api/v1/currencies/{id}
Body: { "name": "string", "symbol": "string", ... }
Response: { "currency": {...} }

POST /api/v1/currencies/convert
Body: { "from_currency": "string", "to_currency": "string", "amount": number, "location_id": "uuid" }
Response: { "converted_amount": number, "rate": number, "rate_date": "date" }
```

## Module Interactions

### Data Flow Example: Transaction Processing

1. **Frontend** → **Transaction Module**: Create transaction request
2. **Transaction Module** → **Customer Module**: Validate customer
3. **Transaction Module** → **Exchange Rate Module**: Get current rates
4. **Transaction Module** → **Ledger Module**: Create ledger entries
5. **Ledger Module** → **Balance Module**: Update balances
6. **Transaction Module** → **Notification Module**: Send notifications
7. **Transaction Module** → **File Module**: Store receipts

### Error Handling

All modules implement consistent error handling:
- HTTP status codes (400, 401, 403, 404, 422, 500)
- Standardized error response format
- Detailed error messages for debugging
- Audit logging for all errors

### Security

All modules implement:
- JWT-based authentication
- Role-based authorization
- Input validation and sanitization
- Rate limiting
- Audit logging

This module map provides a comprehensive overview of the system's architecture and API design, ensuring clear separation of concerns and well-defined interfaces between components.
