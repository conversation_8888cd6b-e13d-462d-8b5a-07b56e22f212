<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .module-name { font-family: Arial, sans-serif; font-size: 12px; font-weight: bold; fill: #ffffff; }
      .flow-label { font-family: Arial, sans-serif; font-size: 10px; fill: #34495e; }
      .step-number { font-family: Arial, sans-serif; font-size: 10px; font-weight: bold; fill: #ffffff; }
      .frontend { fill: #3498db; stroke: #2980b9; stroke-width: 2; }
      .auth { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; }
      .business { fill: #2ecc71; stroke: #27ae60; stroke-width: 2; }
      .data { fill: #f39c12; stroke: #e67e22; stroke-width: 2; }
      .external { fill: #9b59b6; stroke: #8e44ad; stroke-width: 2; }
      .flow-arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-flow { stroke: #e67e22; stroke-width: 2; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 5,5; }
      .notification-flow { stroke: #9b59b6; stroke-width: 2; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 3,3; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="600" y="30" text-anchor="middle" class="title">Module Interaction and Data Flow Diagram</text>
  
  <!-- Frontend Layer -->
  <rect x="50" y="60" width="1100" height="80" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="5"/>
  <text x="70" y="80" class="flow-label" font-weight="bold">Frontend Layer</text>
  
  <!-- Web Application -->
  <rect x="100" y="90" width="120" height="40" class="frontend" rx="5"/>
  <text x="160" y="113" text-anchor="middle" class="module-name">Web App</text>
  
  <!-- Mobile App -->
  <rect x="250" y="90" width="120" height="40" class="frontend" rx="5"/>
  <text x="310" y="113" text-anchor="middle" class="module-name">Mobile App</text>
  
  <!-- Admin Dashboard -->
  <rect x="400" y="90" width="120" height="40" class="frontend" rx="5"/>
  <text x="460" y="113" text-anchor="middle" class="module-name">Admin Dashboard</text>
  
  <!-- API Gateway -->
  <rect x="450" y="160" width="200" height="40" class="auth" rx="5"/>
  <text x="550" y="183" text-anchor="middle" class="module-name">API Gateway</text>
  
  <!-- Authentication Module -->
  <rect x="50" y="240" width="120" height="60" class="auth" rx="5"/>
  <text x="110" y="265" text-anchor="middle" class="module-name">Authentication</text>
  <text x="110" y="280" text-anchor="middle" class="module-name">Module</text>
  
  <!-- Business Logic Layer -->
  <rect x="200" y="220" width="800" height="200" fill="#eafaf1" stroke="#27ae60" stroke-width="1" rx="5"/>
  <text x="220" y="240" class="flow-label" font-weight="bold">Business Logic Layer</text>
  
  <!-- Customer Module -->
  <rect x="220" y="260" width="100" height="50" class="business" rx="5"/>
  <text x="270" y="280" text-anchor="middle" class="module-name">Customer</text>
  <text x="270" y="295" text-anchor="middle" class="module-name">Module</text>
  
  <!-- Transaction Module -->
  <rect x="340" y="260" width="100" height="50" class="business" rx="5"/>
  <text x="390" y="280" text-anchor="middle" class="module-name">Transaction</text>
  <text x="390" y="295" text-anchor="middle" class="module-name">Module</text>
  
  <!-- Ledger Module -->
  <rect x="460" y="260" width="100" height="50" class="business" rx="5"/>
  <text x="510" y="280" text-anchor="middle" class="module-name">Ledger</text>
  <text x="510" y="295" text-anchor="middle" class="module-name">Module</text>
  
  <!-- Exchange Rate Module -->
  <rect x="580" y="260" width="100" height="50" class="business" rx="5"/>
  <text x="630" y="280" text-anchor="middle" class="module-name">Exchange Rate</text>
  <text x="630" y="295" text-anchor="middle" class="module-name">Module</text>
  
  <!-- Reporting Module -->
  <rect x="700" y="260" width="100" height="50" class="business" rx="5"/>
  <text x="750" y="280" text-anchor="middle" class="module-name">Reporting</text>
  <text x="750" y="295" text-anchor="middle" class="module-name">Module</text>
  
  <!-- File Module -->
  <rect x="220" y="330" width="100" height="50" class="business" rx="5"/>
  <text x="270" y="350" text-anchor="middle" class="module-name">File</text>
  <text x="270" y="365" text-anchor="middle" class="module-name">Module</text>
  
  <!-- Location Module -->
  <rect x="340" y="330" width="100" height="50" class="business" rx="5"/>
  <text x="390" y="350" text-anchor="middle" class="module-name">Location</text>
  <text x="390" y="365" text-anchor="middle" class="module-name">Module</text>
  
  <!-- Currency Module -->
  <rect x="460" y="330" width="100" height="50" class="business" rx="5"/>
  <text x="510" y="350" text-anchor="middle" class="module-name">Currency</text>
  <text x="510" y="365" text-anchor="middle" class="module-name">Module</text>
  
  <!-- Notification Module -->
  <rect x="580" y="330" width="100" height="50" class="business" rx="5"/>
  <text x="630" y="350" text-anchor="middle" class="module-name">Notification</text>
  <text x="630" y="365" text-anchor="middle" class="module-name">Module</text>
  
  <!-- Balance Module -->
  <rect x="700" y="330" width="100" height="50" class="business" rx="5"/>
  <text x="750" y="350" text-anchor="middle" class="module-name">Balance</text>
  <text x="750" y="365" text-anchor="middle" class="module-name">Module</text>
  
  <!-- Data Layer -->
  <rect x="50" y="450" width="700" height="120" fill="#fef9e7" stroke="#f39c12" stroke-width="1" rx="5"/>
  <text x="70" y="470" class="flow-label" font-weight="bold">Data Layer</text>
  
  <!-- PostgreSQL -->
  <rect x="100" y="490" width="120" height="60" class="data" rx="5"/>
  <text x="160" y="515" text-anchor="middle" class="module-name">PostgreSQL</text>
  <text x="160" y="530" text-anchor="middle" class="module-name">Database</text>
  
  <!-- Redis Cache -->
  <rect x="250" y="490" width="120" height="60" class="data" rx="5"/>
  <text x="310" y="515" text-anchor="middle" class="module-name">Redis</text>
  <text x="310" y="530" text-anchor="middle" class="module-name">Cache</text>
  
  <!-- File Storage -->
  <rect x="400" y="490" width="120" height="60" class="data" rx="5"/>
  <text x="460" y="515" text-anchor="middle" class="module-name">File</text>
  <text x="460" y="530" text-anchor="middle" class="module-name">Storage</text>
  
  <!-- Search Engine -->
  <rect x="550" y="490" width="120" height="60" class="data" rx="5"/>
  <text x="610" y="515" text-anchor="middle" class="module-name">Elasticsearch</text>
  <text x="610" y="530" text-anchor="middle" class="module-name">Search</text>
  
  <!-- External Services -->
  <rect x="800" y="450" width="350" height="120" fill="#f4ecf7" stroke="#8e44ad" stroke-width="1" rx="5"/>
  <text x="820" y="470" class="flow-label" font-weight="bold">External Services</text>
  
  <!-- WhatsApp API -->
  <rect x="820" y="490" width="100" height="30" class="external" rx="5"/>
  <text x="870" y="508" text-anchor="middle" class="module-name">WhatsApp API</text>
  
  <!-- Banking APIs -->
  <rect x="940" y="490" width="100" height="30" class="external" rx="5"/>
  <text x="990" y="508" text-anchor="middle" class="module-name">Banking APIs</text>
  
  <!-- Rate Feeds -->
  <rect x="1060" y="490" width="80" height="30" class="external" rx="5"/>
  <text x="1100" y="508" text-anchor="middle" class="module-name">Rate Feeds</text>
  
  <!-- SWIFT/SATNA -->
  <rect x="820" y="530" width="100" height="30" class="external" rx="5"/>
  <text x="870" y="548" text-anchor="middle" class="module-name">SWIFT/SATNA</text>
  
  <!-- Backup Services -->
  <rect x="940" y="530" width="100" height="30" class="external" rx="5"/>
  <text x="990" y="548" text-anchor="middle" class="module-name">Backup</text>
  
  <!-- Transaction Processing Flow -->
  <g id="transaction-flow">
    <!-- Step numbers -->
    <circle cx="160" cy="140" r="12" fill="#e74c3c"/>
    <text x="160" y="145" text-anchor="middle" class="step-number">1</text>
    
    <circle cx="390" cy="220" r="12" fill="#e74c3c"/>
    <text x="390" y="225" text-anchor="middle" class="step-number">2</text>
    
    <circle cx="270" cy="240" r="12" fill="#e74c3c"/>
    <text x="270" y="245" text-anchor="middle" class="step-number">3</text>
    
    <circle cx="630" cy="240" r="12" fill="#e74c3c"/>
    <text x="630" y="245" text-anchor="middle" class="step-number">4</text>
    
    <circle cx="510" cy="240" r="12" fill="#e74c3c"/>
    <text x="510" y="245" text-anchor="middle" class="step-number">5</text>
    
    <circle cx="750" cy="310" r="12" fill="#e74c3c"/>
    <text x="750" y="315" text-anchor="middle" class="step-number">6</text>
    
    <circle cx="630" cy="310" r="12" fill="#e74c3c"/>
    <text x="630" y="315" text-anchor="middle" class="step-number">7</text>
    
    <circle cx="270" cy="310" r="12" fill="#e74c3c"/>
    <text x="270" y="315" text-anchor="middle" class="step-number">8</text>
  </g>
  
  <!-- Flow Arrows -->
  <!-- Frontend to API Gateway -->
  <line x1="160" y1="130" x2="450" y2="180" class="flow-arrow"/>
  <line x1="310" y1="130" x2="500" y2="180" class="flow-arrow"/>
  <line x1="460" y1="130" x2="550" y2="180" class="flow-arrow"/>
  
  <!-- API Gateway to Auth -->
  <line x1="450" y1="180" x2="170" y2="240" class="flow-arrow"/>
  
  <!-- API Gateway to Business Logic -->
  <line x1="550" y1="200" x2="390" y2="260" class="flow-arrow"/>
  
  <!-- Transaction Module Interactions -->
  <line x1="340" y1="285" x2="320" y2="285" class="flow-arrow"/>
  <line x1="440" y1="285" x2="460" y2="285" class="flow-arrow"/>
  <line x1="440" y1="285" x2="580" y2="285" class="flow-arrow"/>
  
  <!-- Ledger to Balance -->
  <line x1="560" y1="285" x2="700" y2="355" class="flow-arrow"/>
  
  <!-- Notification Flow -->
  <line x1="580" y1="355" x2="820" y2="505" class="notification-flow"/>
  
  <!-- File Storage Flow -->
  <line x1="270" y1="380" x2="460" y2="490" class="data-flow"/>
  
  <!-- Database Connections -->
  <line x1="270" y1="310" x2="160" y2="490" class="data-flow"/>
  <line x1="390" y1="310" x2="160" y2="490" class="data-flow"/>
  <line x1="510" y1="310" x2="160" y2="490" class="data-flow"/>
  <line x1="750" y1="310" x2="160" y2="490" class="data-flow"/>
  
  <!-- Cache Connections -->
  <line x1="390" y1="310" x2="310" y2="490" class="data-flow"/>
  <line x1="630" y1="310" x2="310" y2="490" class="data-flow"/>
  
  <!-- External API Connections -->
  <line x1="630" y1="310" x2="990" y2="490" class="notification-flow"/>
  <line x1="630" y1="260" x2="1100" y2="490" class="notification-flow"/>
  
  <!-- Flow Description -->
  <rect x="50" y="600" width="1100" height="280" fill="#ffffff" stroke="#bdc3c7" stroke-width="1" rx="5"/>
  <text x="70" y="620" class="flow-label" font-weight="bold">Transaction Processing Flow Example:</text>
  
  <text x="70" y="645" class="flow-label">1. User initiates transaction through Web/Mobile App</text>
  <text x="70" y="665" class="flow-label">2. API Gateway routes request and validates authentication</text>
  <text x="70" y="685" class="flow-label">3. Transaction Module validates customer information</text>
  <text x="70" y="705" class="flow-label">4. Exchange Rate Module provides current rates</text>
  <text x="70" y="725" class="flow-label">5. Ledger Module creates double-entry bookkeeping records</text>
  <text x="70" y="745" class="flow-label">6. Balance Module updates branch and customer balances</text>
  <text x="70" y="765" class="flow-label">7. Notification Module prepares WhatsApp/Email notifications</text>
  <text x="70" y="785" class="flow-label">8. File Module stores transaction receipts and documents</text>
  
  <text x="70" y="815" class="flow-label" font-weight="bold">Data Flow Types:</text>
  <line x1="70" y1="835" x2="120" y2="835" class="flow-arrow"/>
  <text x="130" y="838" class="flow-label">API Calls and Business Logic Flow</text>
  
  <line x1="70" y1="850" x2="120" y2="850" class="data-flow"/>
  <text x="130" y="853" class="flow-label">Database Operations and Data Persistence</text>
  
  <line x1="70" y1="865" x2="120" y2="865" class="notification-flow"/>
  <text x="130" y="868" class="flow-label">External Service Integration and Notifications</text>
  
  <!-- Module Responsibilities -->
  <text x="600" y="620" class="flow-label" font-weight="bold">Key Module Responsibilities:</text>
  <text x="600" y="645" class="flow-label">• Authentication: JWT tokens, RBAC, session management</text>
  <text x="600" y="665" class="flow-label">• Transaction: Business logic, validation, multi-step processing</text>
  <text x="600" y="685" class="flow-label">• Ledger: Double-entry bookkeeping, audit trail</text>
  <text x="600" y="705" class="flow-label">• Balance: Real-time balance calculations, allocations</text>
  <text x="600" y="725" class="flow-label">• Reporting: Statement generation, PDF/Excel export</text>
  <text x="600" y="745" class="flow-label">• Notification: WhatsApp integration, email alerts</text>
  <text x="600" y="765" class="flow-label">• File: Receipt storage, document management</text>
  <text x="600" y="785" class="flow-label">• Exchange Rate: Rate management, historical tracking</text>
  
  <!-- Performance Notes -->
  <text x="70" y="815" class="flow-label" font-weight="bold">Performance Optimizations:</text>
  <text x="70" y="835" class="flow-label">• Redis caching for frequently accessed data (rates, balances)</text>
  <text x="70" y="850" class="flow-label">• Asynchronous processing for notifications and file operations</text>
  <text x="70" y="865" class="flow-label">• Database connection pooling and query optimization</text>
</svg>
