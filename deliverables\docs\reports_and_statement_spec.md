# Reports and Statement Specification - Exchange Accounting System

## Overview

This document details the comprehensive reporting system for the exchange accounting platform, including statement-of-account formats, field specifications, aggregation rules, and receipt linking mechanisms.

## Statement of Account Format

### Customer Statement of Account

The customer statement follows a standardized format that provides complete transaction history and balance information for a specified period.

#### Header Information
```
EXCHANGE ACCOUNTING SYSTEM
Statement of Account

Customer: [Customer Name] ([Customer Code])
Company: [Company Name if applicable]
Period: [Start Date] to [End Date]
Statement Date: [Generation Date]
Location: [Branch Name, City, Country]
Currency: [Primary Currency Code]
```

#### Opening Balance Section
```
OPENING BALANCE AS OF [Start Date]
Currency    Amount      Sign
USD         1,250.00    Dr
AED         5,000.00    Cr
IRR         0.00        -
```

#### Transaction Details Section

**Column Headers:**
- **Date**: Transaction date (DD/MM/YYYY format)
- **Type**: Transaction type code (JV, TSN, TRQ, DBN, CBS, etc.)
- **Number**: System-generated transaction number
- **Bank Ref**: Bank reference number (if applicable)
- **Narration**: Transaction description
- **FCy**: Foreign currency code
- **Debit**: Debit amount in transaction currency
- **Credit**: Credit amount in transaction currency
- **Balance**: Running balance after transaction
- **Dr/Cr**: Balance sign indicator

**Example Transaction Lines:**
```
Date        Type  Number  Bank Ref    Narration                FCy    Debit      Credit     Balance    Dr/Cr
24/01/2025  JV    8998    -          PLASTEXTPERT DMCC        DHS    -          367,500.00 367,500.00 Cr
24/01/2025  TSN   8999    TRF001     USD PURCHASE             USD    818.00     -          818.00     Dr
25/01/2025  TRQ   9000    -          TRANSFER TO TEHRAN       USD    -          200.00     618.00     Dr
25/01/2025  DBN   9001    DBN001     DELIVERY CHARGES         USD    25.00      -          643.00     Dr
```

#### Fees and Charges Section
```
FEES AND CHARGES SUMMARY
Type                Amount      Currency
Exchange Fee        0.25%       USD
Delivery Fee        25.00       USD
Transfer Fee        10.00       USD
Total Fees          45.25       USD
```

#### Closing Balance Section
```
CLOSING BALANCE AS OF [End Date]
Currency    Amount      Sign
USD         643.00      Dr
AED         367,500.00  Cr
IRR         0.00        -

BALANCE CARRIED FORWARD
USD         643.00      Dr
```

#### Receipt References Section
```
ATTACHED RECEIPTS
Receipt ID    Date        Type        Description              File
RCP001       24/01/2025  Delivery    Signed delivery receipt  delivery_001.pdf
RCP002       25/01/2025  Transfer    Bank transfer slip       transfer_001.jpg
```

### Branch Statement of Account

#### Branch Balance Summary
```
BRANCH BALANCE SUMMARY - [Branch Name]
As of [Date]

Currency    Total Balance    Company Float    Customer Allocations
USD         10,000.00       2,000.00         8,000.00
AED         500,000.00      100,000.00       400,000.00
IRR         50,000,000.00   10,000,000.00    40,000,000.00
```

#### Customer Allocation Breakdown
```
CUSTOMER ALLOCATION BREAKDOWN - USD
Customer Code    Customer Name        Allocated Amount    Last Transaction
CUST001         Ahmad Ali            2,500.00            TXN-2025-001
CUST002         Sara Hassan          3,200.00            TXN-2025-015
CUST003         Omar Khalil          2,300.00            TXN-2025-008
Total Allocated                      8,000.00
```

## Field Specifications

### Transaction Type Codes

| Code | Description | Usage |
|------|-------------|-------|
| JV | Journal Voucher | General journal entries |
| TSN | Transaction | Standard buy/sell transactions |
| TRQ | Transfer Request | Internal transfers |
| DBN | Debit Note | Charges and fees |
| CBS | Cash Book | Cash transactions |
| BNK | Bank Entry | Bank-related transactions |
| ADJ | Adjustment | Correction entries |
| REV | Reversal | Transaction reversals |

### Currency Display Rules

1. **Amount Formatting**: 
   - USD: 2 decimal places (1,234.56)
   - AED: 2 decimal places (1,234.56)
   - IRR: 0 decimal places (1,234,567)

2. **Sign Convention**:
   - Dr (Debit): Customer owes money or has negative balance
   - Cr (Credit): Customer has positive balance or credit
   - Balance of 0.00 shows as "-"

3. **Currency Conversion**:
   - All amounts shown in original transaction currency
   - Converted amounts shown in parentheses when applicable
   - Exchange rates displayed with 6 decimal places

### Date and Time Formatting

- **Statement Dates**: DD/MM/YYYY format
- **Transaction Times**: HH:MM:SS (24-hour format)
- **Timestamps**: ISO 8601 format for system records
- **Period Ranges**: "From DD/MM/YYYY to DD/MM/YYYY"

## Aggregation Rules

### Balance Calculations

#### Running Balance Algorithm
```python
def calculate_running_balance(transactions, opening_balance):
    balance = opening_balance
    for transaction in transactions:
        if transaction.type in ['debit', 'charge', 'fee']:
            balance += transaction.amount
        elif transaction.type in ['credit', 'payment', 'receipt']:
            balance -= transaction.amount
        transaction.balance_after = balance
    return balance
```

#### Multi-Currency Aggregation
```python
def aggregate_multi_currency_balance(transactions, base_currency='USD'):
    balances = {}
    for transaction in transactions:
        currency = transaction.currency
        if currency not in balances:
            balances[currency] = 0
        
        if transaction.is_debit:
            balances[currency] += transaction.amount
        else:
            balances[currency] -= transaction.amount
    
    return balances
```

### Fee Calculation Rules

#### Percentage Fees
- Applied to transaction principal amount
- Calculated before currency conversion
- Rounded to currency decimal places
- Minimum fee thresholds apply

#### Fixed Fees
- Applied per transaction regardless of amount
- Currency-specific fee schedules
- Location-based fee variations
- Time-based fee adjustments

### Branch Balance Allocation Rules

#### Allocation Priority
1. **Customer Deposits**: Direct allocation to customer
2. **Customer Withdrawals**: Reduce customer allocation first, then company float
3. **Internal Transfers**: Move allocations between customers
4. **Company Transactions**: Affect company float only

#### Rebalancing Algorithm
```python
def rebalance_allocations(branch_balance, transactions):
    total_allocated = sum(allocation.amount for allocation in branch_balance.allocations)
    available_balance = branch_balance.total_balance - total_allocated
    
    if available_balance < 0:
        # Reduce allocations proportionally
        reduction_factor = branch_balance.total_balance / total_allocated
        for allocation in branch_balance.allocations:
            allocation.amount *= reduction_factor
    
    return branch_balance
```

## Receipt Linking Mechanism

### Receipt Storage Structure
```
/receipts/
  /{year}/
    /{month}/
      /{transaction_id}/
        /original_{filename}
        /thumbnail_{filename}
        /signed_{filename}
```

### Receipt Metadata
```json
{
  "receipt_id": "uuid",
  "transaction_id": "uuid",
  "file_name": "delivery_receipt_001.pdf",
  "file_path": "/receipts/2025/01/txn-uuid/original_delivery_receipt_001.pdf",
  "file_size": 2048576,
  "mime_type": "application/pdf",
  "description": "Signed delivery receipt for USD 1000",
  "is_signed": true,
  "signature_verified": true,
  "uploaded_at": "2025-01-24T10:30:00Z",
  "uploaded_by": "user-uuid",
  "thumbnail_path": "/receipts/2025/01/txn-uuid/thumbnail_delivery_receipt_001.jpg"
}
```

### Receipt Display in Statements

#### PDF Format
- Thumbnail images embedded in statement
- Clickable links to full-size receipts
- QR codes for digital verification
- Receipt summary table

#### HTML Format
- Interactive image gallery
- Zoom and pan functionality
- Download links for original files
- Receipt verification status

#### Excel Format
- Receipt file names in dedicated column
- Hyperlinks to receipt files
- Receipt count per transaction
- Receipt verification flags

## Statement Generation Process

### Data Collection Phase
1. **Period Validation**: Ensure valid date range
2. **Transaction Retrieval**: Get all transactions in period
3. **Balance Calculation**: Compute opening and closing balances
4. **Fee Aggregation**: Summarize all fees and charges
5. **Receipt Gathering**: Collect all related receipts

### Processing Phase
1. **Currency Grouping**: Group transactions by currency
2. **Chronological Sorting**: Sort by transaction date/time
3. **Running Balance**: Calculate balance after each transaction
4. **Fee Allocation**: Distribute fees across transactions
5. **Receipt Linking**: Associate receipts with transactions

### Output Generation Phase
1. **Template Selection**: Choose appropriate template
2. **Data Formatting**: Apply currency and date formatting
3. **Receipt Processing**: Generate thumbnails and links
4. **Export Generation**: Create PDF, HTML, or Excel output
5. **Digital Signature**: Apply digital signature if required

## Export Formats

### PDF Export
- Professional layout with company branding
- Embedded receipt thumbnails
- Digital signature support
- Print-optimized formatting
- Searchable text content

### Excel Export
- Multiple worksheets for different currencies
- Pivot table summaries
- Conditional formatting for balances
- Hyperlinked receipts
- Formula-based calculations

### HTML Export
- Responsive design for all devices
- Interactive receipt gallery
- Sortable transaction tables
- Print stylesheet included
- Accessibility compliant

## Quality Assurance

### Data Validation
- Balance reconciliation checks
- Currency consistency validation
- Date range verification
- Receipt integrity checks
- Mathematical accuracy validation

### Performance Optimization
- Indexed database queries
- Cached balance calculations
- Asynchronous receipt processing
- Paginated large statements
- Compressed export files

This specification ensures consistent, accurate, and comprehensive reporting across all system functions while maintaining audit trail integrity and user accessibility.
