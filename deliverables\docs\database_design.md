# Database Design - Exchange Accounting System

## Overview

This document outlines the comprehensive database schema for the exchange accounting system using a robust ledger-based approach. The design ensures ACID compliance, supports multi-currency operations, and provides complete audit trails for all financial transactions.

## Database Architecture

### Database Engine: PostgreSQL 14+
- **ACID Compliance**: Ensures data consistency and integrity
- **JSON Support**: Flexible metadata storage
- **Advanced Indexing**: GIN, GiST indexes for performance
- **Partitioning**: Table partitioning for large datasets
- **Full-text Search**: Built-in search capabilities

## Core Tables

### 1. Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('admin', 'accountant', 'viewer', 'courier', 'customer')),
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id),
    metadata JSONB DEFAULT '{}'
);

CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
```

### 2. Locations Table
```sql
CREATE TABLE locations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(10) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    city VARCHAR(100) NOT NULL,
    country VARCHAR(100) NOT NULL,
    address TEXT,
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB DEFAULT '{}'
);

CREATE INDEX idx_locations_code ON locations(code);
CREATE INDEX idx_locations_country ON locations(country);
```

### 3. Currencies Table
```sql
CREATE TABLE currencies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(3) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    symbol VARCHAR(10),
    decimal_places INTEGER DEFAULT 2,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_currencies_code ON currencies(code);
```

### 4. Customers Table
```sql
CREATE TABLE customers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_code VARCHAR(20) UNIQUE NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(255),
    company_name VARCHAR(255),
    notes TEXT,
    whatsapp_group_id VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id),
    metadata JSONB DEFAULT '{}'
);

CREATE INDEX idx_customers_code ON customers(customer_code);
CREATE INDEX idx_customers_phone ON customers(phone);
CREATE INDEX idx_customers_name ON customers(first_name, last_name);
```

### 5. Couriers Table
```sql
CREATE TABLE couriers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(10) UNIQUE NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB DEFAULT '{}'
);

CREATE INDEX idx_couriers_code ON couriers(code);
```

### 6. Exchange Rates Table
```sql
CREATE TABLE exchange_rates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    from_currency_id UUID NOT NULL REFERENCES currencies(id),
    to_currency_id UUID NOT NULL REFERENCES currencies(id),
    location_id UUID REFERENCES locations(id),
    buy_rate DECIMAL(18,8) NOT NULL,
    sell_rate DECIMAL(18,8) NOT NULL,
    effective_date DATE NOT NULL,
    effective_time TIME DEFAULT CURRENT_TIME,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id),
    
    CONSTRAINT unique_rate_per_location_date UNIQUE (from_currency_id, to_currency_id, location_id, effective_date)
);

CREATE INDEX idx_exchange_rates_currencies ON exchange_rates(from_currency_id, to_currency_id);
CREATE INDEX idx_exchange_rates_location ON exchange_rates(location_id);
CREATE INDEX idx_exchange_rates_date ON exchange_rates(effective_date);
```

### 7. Transactions Table (Parent)
```sql
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_number VARCHAR(50) UNIQUE NOT NULL,
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('buy', 'sell', 'transfer', 'delivery')),
    customer_id UUID REFERENCES customers(id),
    location_id UUID NOT NULL REFERENCES locations(id),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'cancelled', 'failed')),
    total_amount DECIMAL(18,8),
    currency_id UUID REFERENCES currencies(id),
    exchange_rate DECIMAL(18,8),
    fees_amount DECIMAL(18,8) DEFAULT 0,
    fees_type VARCHAR(10) CHECK (fees_type IN ('fixed', 'percentage')),
    notes TEXT,
    reference_number VARCHAR(100),
    bank_tracking_number VARCHAR(100),
    delivery_method VARCHAR(20) CHECK (delivery_method IN ('cash', 'bank_transfer', 'swift', 'courier')),
    courier_id UUID REFERENCES couriers(id),
    delivery_status VARCHAR(20) CHECK (delivery_status IN ('pending', 'in_transit', 'delivered', 'failed')),
    transaction_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID NOT NULL REFERENCES users(id),
    metadata JSONB DEFAULT '{}'
);

CREATE INDEX idx_transactions_number ON transactions(transaction_number);
CREATE INDEX idx_transactions_customer ON transactions(customer_id);
CREATE INDEX idx_transactions_location ON transactions(location_id);
CREATE INDEX idx_transactions_date ON transactions(transaction_date);
CREATE INDEX idx_transactions_type ON transactions(transaction_type);
CREATE INDEX idx_transactions_status ON transactions(status);
```

### 8. Transaction Lines Table
```sql
CREATE TABLE transaction_lines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id UUID NOT NULL REFERENCES transactions(id) ON DELETE CASCADE,
    line_number INTEGER NOT NULL,
    description TEXT NOT NULL,
    currency_id UUID NOT NULL REFERENCES currencies(id),
    amount DECIMAL(18,8) NOT NULL,
    exchange_rate DECIMAL(18,8),
    converted_amount DECIMAL(18,8),
    converted_currency_id UUID REFERENCES currencies(id),
    line_type VARCHAR(20) NOT NULL CHECK (line_type IN ('principal', 'fee', 'tax')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT unique_line_per_transaction UNIQUE (transaction_id, line_number)
);

CREATE INDEX idx_transaction_lines_transaction ON transaction_lines(transaction_id);
CREATE INDEX idx_transaction_lines_currency ON transaction_lines(currency_id);
```

### 9. Ledger Entries Table (Double-Entry Bookkeeping)
```sql
CREATE TABLE ledger_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id UUID NOT NULL REFERENCES transactions(id),
    transaction_line_id UUID REFERENCES transaction_lines(id),
    account_type VARCHAR(20) NOT NULL CHECK (account_type IN ('asset', 'liability', 'equity', 'revenue', 'expense')),
    account_code VARCHAR(20) NOT NULL,
    account_name VARCHAR(100) NOT NULL,
    currency_id UUID NOT NULL REFERENCES currencies(id),
    location_id UUID NOT NULL REFERENCES locations(id),
    customer_id UUID REFERENCES customers(id),
    debit_amount DECIMAL(18,8) DEFAULT 0,
    credit_amount DECIMAL(18,8) DEFAULT 0,
    balance_after DECIMAL(18,8),
    entry_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    description TEXT,
    reference_number VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID NOT NULL REFERENCES users(id),
    
    CONSTRAINT check_debit_or_credit CHECK ((debit_amount > 0 AND credit_amount = 0) OR (debit_amount = 0 AND credit_amount > 0))
);

CREATE INDEX idx_ledger_entries_transaction ON ledger_entries(transaction_id);
CREATE INDEX idx_ledger_entries_account ON ledger_entries(account_code);
CREATE INDEX idx_ledger_entries_currency_location ON ledger_entries(currency_id, location_id);
CREATE INDEX idx_ledger_entries_customer ON ledger_entries(customer_id);
CREATE INDEX idx_ledger_entries_date ON ledger_entries(entry_date);
```

### 10. Branch Balances Table
```sql
CREATE TABLE branch_balances (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    location_id UUID NOT NULL REFERENCES locations(id),
    currency_id UUID NOT NULL REFERENCES currencies(id),
    total_balance DECIMAL(18,8) NOT NULL DEFAULT 0,
    available_balance DECIMAL(18,8) NOT NULL DEFAULT 0,
    allocated_balance DECIMAL(18,8) NOT NULL DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT unique_branch_currency UNIQUE (location_id, currency_id),
    CONSTRAINT check_balance_consistency CHECK (total_balance = available_balance + allocated_balance)
);

CREATE INDEX idx_branch_balances_location ON branch_balances(location_id);
CREATE INDEX idx_branch_balances_currency ON branch_balances(currency_id);
```

### 11. Branch Balance Allocations Table
```sql
CREATE TABLE branch_balance_allocations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    branch_balance_id UUID NOT NULL REFERENCES branch_balances(id),
    customer_id UUID NOT NULL REFERENCES customers(id),
    allocated_amount DECIMAL(18,8) NOT NULL DEFAULT 0,
    last_transaction_id UUID REFERENCES transactions(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT unique_allocation_per_customer UNIQUE (branch_balance_id, customer_id),
    CONSTRAINT check_positive_allocation CHECK (allocated_amount >= 0)
);

CREATE INDEX idx_balance_allocations_branch ON branch_balance_allocations(branch_balance_id);
CREATE INDEX idx_balance_allocations_customer ON branch_balance_allocations(customer_id);
```

### 12. Receipts Table
```sql
CREATE TABLE receipts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id UUID NOT NULL REFERENCES transactions(id),
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    description TEXT,
    is_signed BOOLEAN DEFAULT false,
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    uploaded_by UUID NOT NULL REFERENCES users(id)
);

CREATE INDEX idx_receipts_transaction ON receipts(transaction_id);
```

### 13. Internal Transfers Table
```sql
CREATE TABLE internal_transfers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id UUID NOT NULL REFERENCES transactions(id),
    from_customer_id UUID NOT NULL REFERENCES customers(id),
    to_customer_id UUID NOT NULL REFERENCES customers(id),
    amount DECIMAL(18,8) NOT NULL,
    currency_id UUID NOT NULL REFERENCES currencies(id),
    transfer_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'pending',
    notes TEXT
);

CREATE INDEX idx_internal_transfers_from_customer ON internal_transfers(from_customer_id);
CREATE INDEX idx_internal_transfers_to_customer ON internal_transfers(to_customer_id);
```

### 14. SWIFT Records Table
```sql
CREATE TABLE swift_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id UUID NOT NULL REFERENCES transactions(id),
    swift_code VARCHAR(11) NOT NULL,
    sender_bank VARCHAR(255),
    receiver_bank VARCHAR(255),
    swift_reference VARCHAR(100),
    message_type VARCHAR(10),
    amount DECIMAL(18,8),
    currency_id UUID REFERENCES currencies(id),
    value_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_swift_records_transaction ON swift_records(transaction_id);
CREATE INDEX idx_swift_records_reference ON swift_records(swift_reference);
```

### 15. Bank Transfers Table
```sql
CREATE TABLE bank_transfers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id UUID NOT NULL REFERENCES transactions(id),
    transfer_type VARCHAR(20) CHECK (transfer_type IN ('SATNA', 'PAYA', 'ACH', 'WIRE')),
    sender_account VARCHAR(50),
    receiver_account VARCHAR(50),
    bank_reference VARCHAR(100),
    amount DECIMAL(18,8),
    currency_id UUID REFERENCES currencies(id),
    transfer_date DATE,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_bank_transfers_transaction ON bank_transfers(transaction_id);
CREATE INDEX idx_bank_transfers_reference ON bank_transfers(bank_reference);
```

## Indexes and Performance Optimization

### Composite Indexes
```sql
-- For statement generation queries
CREATE INDEX idx_ledger_customer_currency_date ON ledger_entries(customer_id, currency_id, entry_date);

-- For balance calculations
CREATE INDEX idx_ledger_location_currency_date ON ledger_entries(location_id, currency_id, entry_date);

-- For transaction searches
CREATE INDEX idx_transactions_customer_date ON transactions(customer_id, transaction_date);
CREATE INDEX idx_transactions_location_date ON transactions(location_id, transaction_date);
```

### Partitioning Strategy
```sql
-- Partition ledger_entries by month for better performance
CREATE TABLE ledger_entries_y2024m01 PARTITION OF ledger_entries
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- Similar partitioning for transactions table
CREATE TABLE transactions_y2024m01 PARTITION OF transactions
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

## Business Logic Constraints

### Triggers for Balance Updates
```sql
-- Trigger to update branch balances after ledger entries
CREATE OR REPLACE FUNCTION update_branch_balances()
RETURNS TRIGGER AS $$
BEGIN
    -- Update branch balance logic here
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_branch_balances
    AFTER INSERT OR UPDATE ON ledger_entries
    FOR EACH ROW EXECUTE FUNCTION update_branch_balances();
```

### Data Integrity Rules

1. **Double-Entry Validation**: Every transaction must have balanced debits and credits
2. **Balance Consistency**: Branch total balance must equal sum of allocations plus available balance
3. **Currency Consistency**: All amounts in a transaction must use consistent currency references
4. **Date Validation**: Transaction dates cannot be in the future
5. **Status Transitions**: Transactions can only move through valid status transitions

## Backup and Maintenance

### Backup Strategy
- **Full Backup**: Daily at 2 AM
- **Incremental Backup**: Every 4 hours
- **Transaction Log Backup**: Every 15 minutes
- **Retention**: 30 days online, 1 year archived

### Maintenance Tasks
- **Statistics Update**: Weekly
- **Index Rebuild**: Monthly
- **Partition Maintenance**: Automated monthly partition creation
- **Archive Old Data**: Quarterly archival of data older than 2 years

## Security Considerations

### Row-Level Security
```sql
-- Enable RLS for sensitive tables
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;

-- Create policies based on user roles
CREATE POLICY customer_access_policy ON customers
    FOR ALL TO application_role
    USING (created_by = current_user_id() OR has_customer_access(id));
```

### Encryption
- **At Rest**: Transparent Data Encryption (TDE) for sensitive columns
- **In Transit**: SSL/TLS for all connections
- **Application Level**: AES-256 encryption for PII data

This database design provides a robust foundation for the exchange accounting system with complete audit trails, multi-currency support, and scalable architecture.
