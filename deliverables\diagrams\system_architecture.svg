<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .layer-title { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .component { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .frontend { fill: #3498db; stroke: #2980b9; stroke-width: 2; }
      .api { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; }
      .backend { fill: #2ecc71; stroke: #27ae60; stroke-width: 2; }
      .database { fill: #f39c12; stroke: #e67e22; stroke-width: 2; }
      .external { fill: #9b59b6; stroke: #8e44ad; stroke-width: 2; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="600" y="30" text-anchor="middle" class="title">Exchange Accounting System - Architecture</text>
  
  <!-- Frontend Layer -->
  <rect x="50" y="60" width="1100" height="120" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="5"/>
  <text x="70" y="80" class="layer-title">Frontend Layer</text>
  
  <!-- Web Application -->
  <rect x="80" y="100" width="200" height="60" class="frontend" rx="5"/>
  <text x="180" y="120" text-anchor="middle" class="component">Web Application</text>
  <text x="180" y="135" text-anchor="middle" class="small-text">React/Vue.js</text>
  <text x="180" y="150" text-anchor="middle" class="small-text">Responsive, Multi-language</text>
  
  <!-- Mobile App -->
  <rect x="320" y="100" width="150" height="60" class="frontend" rx="5"/>
  <text x="395" y="120" text-anchor="middle" class="component">Mobile App</text>
  <text x="395" y="135" text-anchor="middle" class="small-text">iOS/Android</text>
  <text x="395" y="150" text-anchor="middle" class="small-text">PWA Support</text>
  
  <!-- Admin Dashboard -->
  <rect x="500" y="100" width="150" height="60" class="frontend" rx="5"/>
  <text x="575" y="120" text-anchor="middle" class="component">Admin Dashboard</text>
  <text x="575" y="135" text-anchor="middle" class="small-text">Analytics</text>
  <text x="575" y="150" text-anchor="middle" class="small-text">Reporting</text>
  
  <!-- API Gateway Layer -->
  <rect x="50" y="200" width="1100" height="80" fill="#fdf2e9" stroke="#e67e22" stroke-width="1" rx="5"/>
  <text x="70" y="220" class="layer-title">API Gateway Layer</text>
  
  <!-- API Gateway -->
  <rect x="400" y="240" width="400" height="30" class="api" rx="5"/>
  <text x="600" y="258" text-anchor="middle" class="component">API Gateway (Kong/AWS)</text>
  
  <!-- Backend Services Layer -->
  <rect x="50" y="300" width="1100" height="200" fill="#eafaf1" stroke="#27ae60" stroke-width="1" rx="5"/>
  <text x="70" y="320" class="layer-title">Backend Services (Microservices)</text>
  
  <!-- Authentication Service -->
  <rect x="80" y="340" width="120" height="50" class="backend" rx="5"/>
  <text x="140" y="360" text-anchor="middle" class="component">Auth Service</text>
  <text x="140" y="375" text-anchor="middle" class="small-text">JWT, RBAC</text>
  
  <!-- Customer Service -->
  <rect x="220" y="340" width="120" height="50" class="backend" rx="5"/>
  <text x="280" y="360" text-anchor="middle" class="component">Customer</text>
  <text x="280" y="375" text-anchor="middle" class="small-text">Management</text>
  
  <!-- Transaction Service -->
  <rect x="360" y="340" width="120" height="50" class="backend" rx="5"/>
  <text x="420" y="360" text-anchor="middle" class="component">Transaction</text>
  <text x="420" y="375" text-anchor="middle" class="small-text">Processing</text>
  
  <!-- Ledger Service -->
  <rect x="500" y="340" width="120" height="50" class="backend" rx="5"/>
  <text x="560" y="360" text-anchor="middle" class="component">Ledger</text>
  <text x="560" y="375" text-anchor="middle" class="small-text">Double-Entry</text>
  
  <!-- Exchange Rate Service -->
  <rect x="640" y="340" width="120" height="50" class="backend" rx="5"/>
  <text x="700" y="360" text-anchor="middle" class="component">Exchange Rate</text>
  <text x="700" y="375" text-anchor="middle" class="small-text">Management</text>
  
  <!-- Reporting Service -->
  <rect x="780" y="340" width="120" height="50" class="backend" rx="5"/>
  <text x="840" y="360" text-anchor="middle" class="component">Reporting</text>
  <text x="840" y="375" text-anchor="middle" class="small-text">PDF/Excel</text>
  
  <!-- Notification Service -->
  <rect x="920" y="340" width="120" height="50" class="backend" rx="5"/>
  <text x="980" y="360" text-anchor="middle" class="component">Notification</text>
  <text x="980" y="375" text-anchor="middle" class="small-text">WhatsApp/Email</text>
  
  <!-- File Service -->
  <rect x="220" y="410" width="120" height="50" class="backend" rx="5"/>
  <text x="280" y="430" text-anchor="middle" class="component">File</text>
  <text x="280" y="445" text-anchor="middle" class="small-text">Management</text>
  
  <!-- Location Service -->
  <rect x="360" y="410" width="120" height="50" class="backend" rx="5"/>
  <text x="420" y="430" text-anchor="middle" class="component">Location</text>
  <text x="420" y="445" text-anchor="middle" class="small-text">Multi-Branch</text>
  
  <!-- Currency Service -->
  <rect x="500" y="410" width="120" height="50" class="backend" rx="5"/>
  <text x="560" y="430" text-anchor="middle" class="component">Currency</text>
  <text x="560" y="445" text-anchor="middle" class="small-text">Multi-Currency</text>
  
  <!-- Database Layer -->
  <rect x="50" y="520" width="700" height="120" fill="#fef9e7" stroke="#f39c12" stroke-width="1" rx="5"/>
  <text x="70" y="540" class="layer-title">Database Layer</text>
  
  <!-- PostgreSQL -->
  <rect x="80" y="560" width="150" height="60" class="database" rx="5"/>
  <text x="155" y="580" text-anchor="middle" class="component">PostgreSQL</text>
  <text x="155" y="595" text-anchor="middle" class="small-text">Primary Database</text>
  <text x="155" y="610" text-anchor="middle" class="small-text">ACID Compliance</text>
  
  <!-- Redis Cache -->
  <rect x="250" y="560" width="120" height="60" class="database" rx="5"/>
  <text x="310" y="580" text-anchor="middle" class="component">Redis</text>
  <text x="310" y="595" text-anchor="middle" class="small-text">Cache Layer</text>
  <text x="310" y="610" text-anchor="middle" class="small-text">Sessions</text>
  
  <!-- Elasticsearch -->
  <rect x="390" y="560" width="120" height="60" class="database" rx="5"/>
  <text x="450" y="580" text-anchor="middle" class="component">Elasticsearch</text>
  <text x="450" y="595" text-anchor="middle" class="small-text">Search Engine</text>
  <text x="450" y="610" text-anchor="middle" class="small-text">Analytics</text>
  
  <!-- File Storage -->
  <rect x="530" y="560" width="120" height="60" class="database" rx="5"/>
  <text x="590" y="580" text-anchor="middle" class="component">File Storage</text>
  <text x="590" y="595" text-anchor="middle" class="small-text">Receipts</text>
  <text x="590" y="610" text-anchor="middle" class="small-text">Documents</text>
  
  <!-- External Integrations -->
  <rect x="770" y="520" width="380" height="120" fill="#f4ecf7" stroke="#8e44ad" stroke-width="1" rx="5"/>
  <text x="790" y="540" class="layer-title">External Integrations</text>
  
  <!-- WhatsApp API -->
  <rect x="800" y="560" width="100" height="30" class="external" rx="5"/>
  <text x="850" y="578" text-anchor="middle" class="component">WhatsApp API</text>
  
  <!-- Banking APIs -->
  <rect x="920" y="560" width="100" height="30" class="external" rx="5"/>
  <text x="970" y="578" text-anchor="middle" class="component">Banking APIs</text>
  
  <!-- Rate Feeds -->
  <rect x="1040" y="560" width="100" height="30" class="external" rx="5"/>
  <text x="1090" y="578" text-anchor="middle" class="component">Rate Feeds</text>
  
  <!-- SWIFT/SATNA -->
  <rect x="800" y="600" width="100" height="30" class="external" rx="5"/>
  <text x="850" y="618" text-anchor="middle" class="component">SWIFT/SATNA</text>
  
  <!-- Backup Services -->
  <rect x="920" y="600" width="100" height="30" class="external" rx="5"/>
  <text x="970" y="618" text-anchor="middle" class="component">Backup</text>
  
  <!-- Monitoring -->
  <rect x="1040" y="600" width="100" height="30" class="external" rx="5"/>
  <text x="1090" y="618" text-anchor="middle" class="component">Monitoring</text>
  
  <!-- Infrastructure Layer -->
  <rect x="50" y="660" width="1100" height="80" fill="#f8f9fa" stroke="#6c757d" stroke-width="1" rx="5"/>
  <text x="70" y="680" class="layer-title">Infrastructure Layer</text>
  
  <!-- Windows Server -->
  <rect x="200" y="700" width="150" height="30" fill="#6c757d" stroke="#495057" stroke-width="2" rx="5"/>
  <text x="275" y="718" text-anchor="middle" class="component" fill="white">Windows Server</text>
  
  <!-- Docker/K8s -->
  <rect x="370" y="700" width="150" height="30" fill="#6c757d" stroke="#495057" stroke-width="2" rx="5"/>
  <text x="445" y="718" text-anchor="middle" class="component" fill="white">Docker/Kubernetes</text>
  
  <!-- Load Balancer -->
  <rect x="540" y="700" width="150" height="30" fill="#6c757d" stroke="#495057" stroke-width="2" rx="5"/>
  <text x="615" y="718" text-anchor="middle" class="component" fill="white">Load Balancer</text>
  
  <!-- Security -->
  <rect x="710" y="700" width="150" height="30" fill="#6c757d" stroke="#495057" stroke-width="2" rx="5"/>
  <text x="785" y="718" text-anchor="middle" class="component" fill="white">Security Layer</text>
  
  <!-- Data Flow Arrows -->
  <line x1="180" y1="160" x2="180" y2="240" class="arrow"/>
  <line x1="395" y1="160" x2="395" y2="240" class="arrow"/>
  <line x1="575" y1="160" x2="575" y2="240" class="arrow"/>
  
  <line x1="600" y1="270" x2="600" y2="340" class="arrow"/>
  
  <line x1="420" y1="390" x2="420" y2="560" class="arrow"/>
  <line x1="560" y1="390" x2="560" y2="560" class="arrow"/>
  
  <line x1="980" y1="390" x2="980" y2="560" class="arrow"/>
  
  <!-- Legend -->
  <rect x="50" y="750" width="600" height="40" fill="#ffffff" stroke="#bdc3c7" stroke-width="1" rx="3"/>
  <text x="60" y="765" class="small-text">Legend:</text>
  <rect x="110" y="755" width="15" height="10" class="frontend"/>
  <text x="130" y="763" class="small-text">Frontend</text>
  <rect x="180" y="755" width="15" height="10" class="api"/>
  <text x="200" y="763" class="small-text">API Gateway</text>
  <rect x="270" y="755" width="15" height="10" class="backend"/>
  <text x="290" y="763" class="small-text">Backend Services</text>
  <rect x="380" y="755" width="15" height="10" class="database"/>
  <text x="400" y="763" class="small-text">Database</text>
  <rect x="460" y="755" width="15" height="10" class="external"/>
  <text x="480" y="763" class="small-text">External</text>
  <rect x="530" y="755" width="15" height="10" fill="#6c757d"/>
  <text x="550" y="763" class="small-text">Infrastructure</text>
</svg>
