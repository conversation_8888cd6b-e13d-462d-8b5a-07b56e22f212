<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .table-name { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #ffffff; }
      .field-name { font-family: Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .field-type { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .pk { font-weight: bold; fill: #e74c3c; }
      .fk { font-style: italic; fill: #3498db; }
      .table { fill: #ecf0f1; stroke: #34495e; stroke-width: 2; }
      .table-header { fill: #34495e; }
      .relationship { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .one-to-many { stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="700" y="30" text-anchor="middle" class="title">Exchange Accounting System - Entity Relationship Diagram</text>
  
  <!-- Users Table -->
  <g id="users">
    <rect x="50" y="60" width="180" height="140" class="table" rx="5"/>
    <rect x="50" y="60" width="180" height="25" class="table-header" rx="5"/>
    <text x="140" y="77" text-anchor="middle" class="table-name">users</text>
    <text x="60" y="95" class="field-name pk">id (UUID)</text>
    <text x="60" y="110" class="field-name">username (VARCHAR)</text>
    <text x="60" y="125" class="field-name">email (VARCHAR)</text>
    <text x="60" y="140" class="field-name">password_hash (VARCHAR)</text>
    <text x="60" y="155" class="field-name">first_name (VARCHAR)</text>
    <text x="60" y="170" class="field-name">last_name (VARCHAR)</text>
    <text x="60" y="185" class="field-name">role (VARCHAR)</text>
  </g>
  
  <!-- Customers Table -->
  <g id="customers">
    <rect x="300" y="60" width="180" height="155" class="table" rx="5"/>
    <rect x="300" y="60" width="180" height="25" class="table-header" rx="5"/>
    <text x="390" y="77" text-anchor="middle" class="table-name">customers</text>
    <text x="310" y="95" class="field-name pk">id (UUID)</text>
    <text x="310" y="110" class="field-name">customer_code (VARCHAR)</text>
    <text x="310" y="125" class="field-name">first_name (VARCHAR)</text>
    <text x="310" y="140" class="field-name">last_name (VARCHAR)</text>
    <text x="310" y="155" class="field-name">phone (VARCHAR)</text>
    <text x="310" y="170" class="field-name">email (VARCHAR)</text>
    <text x="310" y="185" class="field-name">company_name (VARCHAR)</text>
    <text x="310" y="200" class="field-name fk">created_by (UUID)</text>
  </g>
  
  <!-- Locations Table -->
  <g id="locations">
    <rect x="550" y="60" width="180" height="140" class="table" rx="5"/>
    <rect x="550" y="60" width="180" height="25" class="table-header" rx="5"/>
    <text x="640" y="77" text-anchor="middle" class="table-name">locations</text>
    <text x="560" y="95" class="field-name pk">id (UUID)</text>
    <text x="560" y="110" class="field-name">code (VARCHAR)</text>
    <text x="560" y="125" class="field-name">name (VARCHAR)</text>
    <text x="560" y="140" class="field-name">city (VARCHAR)</text>
    <text x="560" y="155" class="field-name">country (VARCHAR)</text>
    <text x="560" y="170" class="field-name">address (TEXT)</text>
    <text x="560" y="185" class="field-name">phone (VARCHAR)</text>
  </g>
  
  <!-- Currencies Table -->
  <g id="currencies">
    <rect x="800" y="60" width="180" height="125" class="table" rx="5"/>
    <rect x="800" y="60" width="180" height="25" class="table-header" rx="5"/>
    <text x="890" y="77" text-anchor="middle" class="table-name">currencies</text>
    <text x="810" y="95" class="field-name pk">id (UUID)</text>
    <text x="810" y="110" class="field-name">code (VARCHAR)</text>
    <text x="810" y="125" class="field-name">name (VARCHAR)</text>
    <text x="810" y="140" class="field-name">symbol (VARCHAR)</text>
    <text x="810" y="155" class="field-name">decimal_places (INT)</text>
    <text x="810" y="170" class="field-name">is_active (BOOLEAN)</text>
  </g>
  
  <!-- Transactions Table -->
  <g id="transactions">
    <rect x="50" y="250" width="200" height="200" class="table" rx="5"/>
    <rect x="50" y="250" width="200" height="25" class="table-header" rx="5"/>
    <text x="150" y="267" text-anchor="middle" class="table-name">transactions</text>
    <text x="60" y="285" class="field-name pk">id (UUID)</text>
    <text x="60" y="300" class="field-name">transaction_number (VARCHAR)</text>
    <text x="60" y="315" class="field-name">transaction_type (VARCHAR)</text>
    <text x="60" y="330" class="field-name fk">customer_id (UUID)</text>
    <text x="60" y="345" class="field-name fk">location_id (UUID)</text>
    <text x="60" y="360" class="field-name fk">currency_id (UUID)</text>
    <text x="60" y="375" class="field-name">total_amount (DECIMAL)</text>
    <text x="60" y="390" class="field-name">exchange_rate (DECIMAL)</text>
    <text x="60" y="405" class="field-name">status (VARCHAR)</text>
    <text x="60" y="420" class="field-name">transaction_date (TIMESTAMP)</text>
    <text x="60" y="435" class="field-name fk">created_by (UUID)</text>
  </g>
  
  <!-- Transaction Lines Table -->
  <g id="transaction_lines">
    <rect x="300" y="250" width="200" height="170" class="table" rx="5"/>
    <rect x="300" y="250" width="200" height="25" class="table-header" rx="5"/>
    <text x="400" y="267" text-anchor="middle" class="table-name">transaction_lines</text>
    <text x="310" y="285" class="field-name pk">id (UUID)</text>
    <text x="310" y="300" class="field-name fk">transaction_id (UUID)</text>
    <text x="310" y="315" class="field-name">line_number (INT)</text>
    <text x="310" y="330" class="field-name">description (TEXT)</text>
    <text x="310" y="345" class="field-name fk">currency_id (UUID)</text>
    <text x="310" y="360" class="field-name">amount (DECIMAL)</text>
    <text x="310" y="375" class="field-name">exchange_rate (DECIMAL)</text>
    <text x="310" y="390" class="field-name">converted_amount (DECIMAL)</text>
    <text x="310" y="405" class="field-name">line_type (VARCHAR)</text>
  </g>
  
  <!-- Ledger Entries Table -->
  <g id="ledger_entries">
    <rect x="550" y="250" width="200" height="200" class="table" rx="5"/>
    <rect x="550" y="250" width="200" height="25" class="table-header" rx="5"/>
    <text x="650" y="267" text-anchor="middle" class="table-name">ledger_entries</text>
    <text x="560" y="285" class="field-name pk">id (UUID)</text>
    <text x="560" y="300" class="field-name fk">transaction_id (UUID)</text>
    <text x="560" y="315" class="field-name">account_type (VARCHAR)</text>
    <text x="560" y="330" class="field-name">account_code (VARCHAR)</text>
    <text x="560" y="345" class="field-name fk">currency_id (UUID)</text>
    <text x="560" y="360" class="field-name fk">location_id (UUID)</text>
    <text x="560" y="375" class="field-name fk">customer_id (UUID)</text>
    <text x="560" y="390" class="field-name">debit_amount (DECIMAL)</text>
    <text x="560" y="405" class="field-name">credit_amount (DECIMAL)</text>
    <text x="560" y="420" class="field-name">balance_after (DECIMAL)</text>
    <text x="560" y="435" class="field-name">entry_date (TIMESTAMP)</text>
  </g>
  
  <!-- Exchange Rates Table -->
  <g id="exchange_rates">
    <rect x="800" y="250" width="200" height="155" class="table" rx="5"/>
    <rect x="800" y="250" width="200" height="25" class="table-header" rx="5"/>
    <text x="900" y="267" text-anchor="middle" class="table-name">exchange_rates</text>
    <text x="810" y="285" class="field-name pk">id (UUID)</text>
    <text x="810" y="300" class="field-name fk">from_currency_id (UUID)</text>
    <text x="810" y="315" class="field-name fk">to_currency_id (UUID)</text>
    <text x="810" y="330" class="field-name fk">location_id (UUID)</text>
    <text x="810" y="345" class="field-name">buy_rate (DECIMAL)</text>
    <text x="810" y="360" class="field-name">sell_rate (DECIMAL)</text>
    <text x="810" y="375" class="field-name">effective_date (DATE)</text>
    <text x="810" y="390" class="field-name fk">created_by (UUID)</text>
  </g>
  
  <!-- Branch Balances Table -->
  <g id="branch_balances">
    <rect x="50" y="480" width="200" height="140" class="table" rx="5"/>
    <rect x="50" y="480" width="200" height="25" class="table-header" rx="5"/>
    <text x="150" y="497" text-anchor="middle" class="table-name">branch_balances</text>
    <text x="60" y="515" class="field-name pk">id (UUID)</text>
    <text x="60" y="530" class="field-name fk">location_id (UUID)</text>
    <text x="60" y="545" class="field-name fk">currency_id (UUID)</text>
    <text x="60" y="560" class="field-name">total_balance (DECIMAL)</text>
    <text x="60" y="575" class="field-name">available_balance (DECIMAL)</text>
    <text x="60" y="590" class="field-name">allocated_balance (DECIMAL)</text>
    <text x="60" y="605" class="field-name">last_updated (TIMESTAMP)</text>
  </g>
  
  <!-- Branch Balance Allocations Table -->
  <g id="balance_allocations">
    <rect x="300" y="480" width="220" height="140" class="table" rx="5"/>
    <rect x="300" y="480" width="220" height="25" class="table-header" rx="5"/>
    <text x="410" y="497" text-anchor="middle" class="table-name">branch_balance_allocations</text>
    <text x="310" y="515" class="field-name pk">id (UUID)</text>
    <text x="310" y="530" class="field-name fk">branch_balance_id (UUID)</text>
    <text x="310" y="545" class="field-name fk">customer_id (UUID)</text>
    <text x="310" y="560" class="field-name">allocated_amount (DECIMAL)</text>
    <text x="310" y="575" class="field-name fk">last_transaction_id (UUID)</text>
    <text x="310" y="590" class="field-name">created_at (TIMESTAMP)</text>
    <text x="310" y="605" class="field-name">updated_at (TIMESTAMP)</text>
  </g>
  
  <!-- Receipts Table -->
  <g id="receipts">
    <rect x="550" y="480" width="180" height="140" class="table" rx="5"/>
    <rect x="550" y="480" width="180" height="25" class="table-header" rx="5"/>
    <text x="640" y="497" text-anchor="middle" class="table-name">receipts</text>
    <text x="560" y="515" class="field-name pk">id (UUID)</text>
    <text x="560" y="530" class="field-name fk">transaction_id (UUID)</text>
    <text x="560" y="545" class="field-name">file_name (VARCHAR)</text>
    <text x="560" y="560" class="field-name">file_path (VARCHAR)</text>
    <text x="560" y="575" class="field-name">file_size (INT)</text>
    <text x="560" y="590" class="field-name">is_signed (BOOLEAN)</text>
    <text x="560" y="605" class="field-name fk">uploaded_by (UUID)</text>
  </g>
  
  <!-- Couriers Table -->
  <g id="couriers">
    <rect x="800" y="480" width="180" height="125" class="table" rx="5"/>
    <rect x="800" y="480" width="180" height="25" class="table-header" rx="5"/>
    <text x="890" y="497" text-anchor="middle" class="table-name">couriers</text>
    <text x="810" y="515" class="field-name pk">id (UUID)</text>
    <text x="810" y="530" class="field-name">code (VARCHAR)</text>
    <text x="810" y="545" class="field-name">first_name (VARCHAR)</text>
    <text x="810" y="560" class="field-name">last_name (VARCHAR)</text>
    <text x="810" y="575" class="field-name">phone (VARCHAR)</text>
    <text x="810" y="590" class="field-name">is_active (BOOLEAN)</text>
  </g>
  
  <!-- Internal Transfers Table -->
  <g id="internal_transfers">
    <rect x="1050" y="250" width="200" height="155" class="table" rx="5"/>
    <rect x="1050" y="250" width="200" height="25" class="table-header" rx="5"/>
    <text x="1150" y="267" text-anchor="middle" class="table-name">internal_transfers</text>
    <text x="1060" y="285" class="field-name pk">id (UUID)</text>
    <text x="1060" y="300" class="field-name fk">transaction_id (UUID)</text>
    <text x="1060" y="315" class="field-name fk">from_customer_id (UUID)</text>
    <text x="1060" y="330" class="field-name fk">to_customer_id (UUID)</text>
    <text x="1060" y="345" class="field-name">amount (DECIMAL)</text>
    <text x="1060" y="360" class="field-name fk">currency_id (UUID)</text>
    <text x="1060" y="375" class="field-name">transfer_date (TIMESTAMP)</text>
    <text x="1060" y="390" class="field-name">status (VARCHAR)</text>
  </g>
  
  <!-- SWIFT Records Table -->
  <g id="swift_records">
    <rect x="1050" y="430" width="200" height="140" class="table" rx="5"/>
    <rect x="1050" y="430" width="200" height="25" class="table-header" rx="5"/>
    <text x="1150" y="447" text-anchor="middle" class="table-name">swift_records</text>
    <text x="1060" y="465" class="field-name pk">id (UUID)</text>
    <text x="1060" y="480" class="field-name fk">transaction_id (UUID)</text>
    <text x="1060" y="495" class="field-name">swift_code (VARCHAR)</text>
    <text x="1060" y="510" class="field-name">sender_bank (VARCHAR)</text>
    <text x="1060" y="525" class="field-name">receiver_bank (VARCHAR)</text>
    <text x="1060" y="540" class="field-name">swift_reference (VARCHAR)</text>
    <text x="1060" y="555" class="field-name">amount (DECIMAL)</text>
  </g>
  
  <!-- Bank Transfers Table -->
  <g id="bank_transfers">
    <rect x="1050" y="590" width="200" height="140" class="table" rx="5"/>
    <rect x="1050" y="590" width="200" height="25" class="table-header" rx="5"/>
    <text x="1150" y="607" text-anchor="middle" class="table-name">bank_transfers</text>
    <text x="1060" y="625" class="field-name pk">id (UUID)</text>
    <text x="1060" y="640" class="field-name fk">transaction_id (UUID)</text>
    <text x="1060" y="655" class="field-name">transfer_type (VARCHAR)</text>
    <text x="1060" y="670" class="field-name">sender_account (VARCHAR)</text>
    <text x="1060" y="685" class="field-name">receiver_account (VARCHAR)</text>
    <text x="1060" y="700" class="field-name">bank_reference (VARCHAR)</text>
    <text x="1060" y="715" class="field-name">amount (DECIMAL)</text>
  </g>
  
  <!-- Relationships -->
  <!-- Users to Customers -->
  <line x1="230" y1="130" x2="300" y2="130" class="relationship"/>
  
  <!-- Customers to Transactions -->
  <line x1="300" y1="200" x2="250" y2="330" class="relationship one-to-many"/>
  
  <!-- Locations to Transactions -->
  <line x1="550" y1="180" x2="250" y2="345" class="relationship one-to-many"/>
  
  <!-- Currencies to Transactions -->
  <line x1="800" y1="180" x2="250" y2="360" class="relationship one-to-many"/>
  
  <!-- Transactions to Transaction Lines -->
  <line x1="250" y1="350" x2="300" y2="350" class="relationship one-to-many"/>
  
  <!-- Transactions to Ledger Entries -->
  <line x1="250" y1="350" x2="550" y2="350" class="relationship one-to-many"/>
  
  <!-- Transactions to Receipts -->
  <line x1="250" y1="400" x2="550" y2="530" class="relationship one-to-many"/>
  
  <!-- Locations to Branch Balances -->
  <line x1="550" y1="200" x2="150" y2="480" class="relationship one-to-many"/>
  
  <!-- Currencies to Branch Balances -->
  <line x1="800" y1="185" x2="150" y2="480" class="relationship one-to-many"/>
  
  <!-- Branch Balances to Allocations -->
  <line x1="250" y1="550" x2="300" y2="550" class="relationship one-to-many"/>
  
  <!-- Customers to Allocations -->
  <line x1="390" y1="215" x2="410" y2="480" class="relationship one-to-many"/>
  
  <!-- Transactions to Internal Transfers -->
  <line x1="250" y1="350" x2="1050" y2="350" class="relationship"/>
  
  <!-- Transactions to SWIFT Records -->
  <line x1="250" y1="400" x2="1050" y2="500" class="relationship"/>
  
  <!-- Transactions to Bank Transfers -->
  <line x1="250" y1="400" x2="1050" y2="650" class="relationship"/>
  
  <!-- Legend -->
  <rect x="50" y="750" width="400" height="80" fill="#ffffff" stroke="#bdc3c7" stroke-width="1" rx="3"/>
  <text x="60" y="770" class="field-name">Legend:</text>
  <text x="60" y="785" class="pk">Primary Key (PK)</text>
  <text x="60" y="800" class="fk">Foreign Key (FK)</text>
  <line x1="200" y1="785" x2="250" y2="785" class="relationship"/>
  <text x="260" y="788" class="field-name">One-to-One/Many-to-One</text>
  <line x1="200" y1="800" x2="250" y2="800" class="relationship one-to-many"/>
  <text x="260" y="803" class="field-name">One-to-Many</text>
  
  <!-- Additional Notes -->
  <text x="50" y="860" class="field-name">Key Features:</text>
  <text x="50" y="880" class="field-type">• Double-entry bookkeeping through ledger_entries table</text>
  <text x="50" y="895" class="field-type">• Multi-currency support with exchange rates</text>
  <text x="50" y="910" class="field-type">• Branch balance allocation tracking</text>
  <text x="50" y="925" class="field-type">• Complete audit trail with timestamps</text>
  <text x="50" y="940" class="field-type">• Receipt and document management</text>
  <text x="50" y="955" class="field-type">• Support for various transfer types (SWIFT, SATNA, etc.)</text>
</svg>
